#!/usr/bin/env python3
"""
Test script to verify that the Telegram manager only processes user addition actions
and ignores other actions like message pinning, title changes, etc.

This script simulates different types of Telegram actions to ensure the fix works correctly.
"""

import asyncio
import logging
from unittest.mock import Mock, AsyncMock
from telethon.tl.types import (
    MessageActionChatAddUser,
    MessageActionPinMessage,
    MessageActionChatEditTitle,
    MessageActionChatEditPhoto,
    MessageActionChatCreate,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockEvent:
    """Mock Telegram event for testing."""

    def __init__(self, action_type, chat_id=12345):
        self.chat_id = chat_id
        self.action_message = Mock()
        self.action_message.action = action_type

        # Also add message.action for NewMessage events
        self.message = Mock()
        self.message.action = action_type
    
    async def get_chat(self):
        chat = Mock()
        chat.id = self.chat_id
        chat.title = f"Test Group {self.chat_id}"
        return chat


class MockClient:
    """Mock Telegram client for testing."""
    
    async def get_me(self):
        me = Mock()
        me.id = *********  # Test user ID
        return me


async def test_action_filtering():
    """Test that only user addition actions are processed."""
    
    # Import the manager (assuming it's in the parent directory)
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from telegram_manager import TelegramAccountManager
    
    manager = TelegramAccountManager()
    manager.target_folder_name = "Test Folder"
    
    # Mock the move_chat_to_folder method to track calls
    move_calls = []
    async def mock_move_chat(client, client_name, chat_id, chat_title):
        move_calls.append((chat_id, chat_title))
        logger.info(f"MOCK: Would move chat {chat_title} (ID: {chat_id})")
        return True
    
    manager.move_chat_to_folder = mock_move_chat
    
    client = MockClient()
    client_name = "TestClient"
    
    # Test cases
    test_cases = [
        {
            "name": "User Addition (should move)",
            "action": MessageActionChatAddUser(users=[*********]),
            "should_move": True
        },
        {
            "name": "Message Pin (should NOT move)",
            "action": MessageActionPinMessage(),
            "should_move": False
        },
        {
            "name": "Title Change (should NOT move)",
            "action": MessageActionChatEditTitle(title="New Title"),
            "should_move": False
        },
        {
            "name": "Photo Change (should NOT move)",
            "action": MessageActionChatEditPhoto(photo=Mock()),
            "should_move": False
        },
        {
            "name": "Group Creation with user (should move)",
            "action": MessageActionChatCreate(title="New Group", users=[*********]),
            "should_move": True
        },
        {
            "name": "User Addition of someone else (should NOT move)",
            "action": MessageActionChatAddUser(users=[987654321]),  # Different user ID
            "should_move": False
        }
    ]
    
    logger.info("🧪 Starting action filtering tests...")
    
    for i, test_case in enumerate(test_cases):
        logger.info(f"\n--- Test {i+1}: {test_case['name']} ---")
        
        # Create mock event
        event = MockEvent(
            action_type=test_case["action"],
            chat_id=1000 + i
        )
        
        # Clear previous calls
        move_calls.clear()
        
        # Run the handler
        try:
            await manager.handle_chat_action(event, client, client_name)
            
            # Check if chat was moved
            was_moved = len(move_calls) > 0
            
            if was_moved == test_case["should_move"]:
                logger.info(f"✅ PASS: Expected move={test_case['should_move']}, got move={was_moved}")
            else:
                logger.error(f"❌ FAIL: Expected move={test_case['should_move']}, got move={was_moved}")
                
        except Exception as e:
            logger.error(f"❌ ERROR in test: {e}")
    
    logger.info("\n🏁 Test completed!")


if __name__ == "__main__":
    asyncio.run(test_action_filtering())
